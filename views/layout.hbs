<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{title}} - Document Management System</title>
  
  <!-- Favicon -->
  <link rel="icon" href="https://bulma.io/favicons/favicon.ico" type="image/x-icon">
  
  <!-- Bulma CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="/stylesheets/style.css">
  
  <!-- CSRF Token for AJAX requests -->
  <meta name="csrf-token" content="{{csrfToken}}">
  
  <!-- Additional head content -->
  {{{_sections.head}}}
</head>
<body>
  <div id="app" class="has-navbar-fixed-top">
    <!-- Navigation -->
    {{> navigation }}
    
    <!-- Flash Messages -->
    {{#if success_msg}}
      <div class="notification is-success is-light is-radiusless mb-0">
        <button class="delete"></button>
        <p>{{success_msg}}</p>
      </div>
    {{/if}}
    
    {{#if error_msg}}
      <div class="notification is-danger is-light is-radiusless mb-0">
        <button class="delete"></button>
        <p>{{error_msg}}</p>
      </div>
    {{/if}}
    
    {{#if error}}
      <div class="notification is-danger is-light is-radiusless mb-0">
        <button class="delete"></button>
        <p>{{error}}</p>
      </div>
    {{/if}}
    
    <!-- Main Content -->
    <main class="main-content">
      {{{body}}}
    </main>
    
    <!-- Footer -->
    <footer class="footer">
      <div class="content has-text-centered">
        <p>
          <strong>Document Management System</strong> by DocManager. The source code is licensed 
          <a href="http://opensource.org/licenses/mit-license.php">MIT</a>.
        </p>
      </div>
    </footer>
    
    <!-- Modals will be loaded here -->
    <div id="modal-container"></div>
  </div>
  
  <!-- Custom JS -->
  <script src="/javascripts/main.js" defer></script>
  
  <!-- Page-specific scripts -->
  {{{_sections.scripts}}}
  
  <!-- Initialize JavaScript -->
  <script>
    // Helper function to format dates
    function formatDate(date, format) {
      if (!date) return '';
      
      const d = new Date(date);
      if (isNaN(d.getTime())) return '';
      
      const pad = (n) => n < 10 ? '0' + n : n;
      
      const formats = {
        'YYYY': d.getFullYear(),
        'MM': pad(d.getMonth() + 1),
        'DD': pad(d.getDate()),
        'HH': pad(d.getHours()),
        'mm': pad(d.getMinutes()),
        'ss': pad(d.getSeconds())
      };
      
      return format.replace(/(YYYY|MM|DD|HH|mm|ss)/g, (match) => formats[match]);
    }
    
    // Make formatDate available to Handlebars
    if (typeof Handlebars !== 'undefined') {
      // Register formatDate helper
      Handlebars.registerHelper('formatDate', function(date, format) {
        return formatDate(date, format);
      });
      
      // Greater than or equal to helper
      Handlebars.registerHelper('gte', function(a, b, options) {
        return a >= b ? options.fn(this) : options.inverse(this);
      });
      
      // Equality helper
      Handlebars.registerHelper('eq', function(a, b, options) {
        return a === b ? options.fn(this) : options.inverse(this);
      });
      
      // Get file icon class based on MIME type
      Handlebars.registerHelper('fileIcon', function(mimeType) {
        const icons = {
          'application/pdf': 'fa-file-pdf has-text-pdf',
          'application/msword': 'fa-file-word has-text-word',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fa-file-word has-text-word',
          'application/vnd.ms-excel': 'fa-file-excel has-text-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fa-file-excel has-text-excel',
          'image/jpeg': 'fa-file-image has-text-image',
          'image/png': 'fa-file-image has-text-image',
          'image/gif': 'fa-file-image has-text-image',
          'application/zip': 'fa-file-archive has-text-archive',
          'application/x-rar-compressed': 'fa-file-archive has-text-archive',
          'application/x-7z-compressed': 'fa-file-archive has-text-archive',
          'text/plain': 'fa-file-alt',
        };
        
        return new Handlebars.SafeString(icons[mimeType] || 'fa-file');
      });
    }
  </script>
</body>
</html>
