{{!< ../layout}}

<div class="columns is-centered">
  <div class="column is-8">
    <div class="box">
      <h1 class="title">{{#if user}}Edit{{else}}Add New{{/if}} User</h1>
      
      {{#if error_msg}}
        <div class="notification is-danger">
          <button class="delete"></button>
          {{error_msg}}
        </div>
      {{/if}}
      
      {{#if errors}}
        <div class="notification is-danger">
          <button class="delete"></button>
          <ul>
            {{#each errors}}
              <li>{{this.msg}}</li>
            {{/each}}
          </ul>
        </div>
      {{/if}}
      
      <form method="POST" action="{{#if user}}/admin/users/{{user.id}}/edit{{else}}/admin/users{{/if}}">
        {{#if user}}
          <input type="hidden" name="_method" value="PUT">
        {{/if}}
        
        <div class="field">
          <label class="label">Name</label>
          <div class="control has-icons-left">
            <input 
              class="input {{#if errors.name}}is-danger{{/if}}" 
              type="text" 
              name="name" 
              placeholder="Full Name"
              value="{{#if user}}{{user.name}}{{else}}{{formData.name}}{{/if}}"
              required
            >
            <span class="icon is-small is-left">
              <i class="fas fa-user"></i>
            </span>
          </div>
          {{#if errors.name}}
            <p class="help is-danger">{{errors.name.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <label class="label">Email</label>
          <div class="control has-icons-left">
            <input 
              class="input {{#if errors.email}}is-danger{{/if}}" 
              type="email" 
              name="email" 
              placeholder="Email address"
              value="{{#if user}}{{user.email}}{{else}}{{formData.email}}{{/if}}"
              {{#if user}}readonly{{else}}required{{/if}}
            >
            <span class="icon is-small is-left">
              <i class="fas fa-envelope"></i>
            </span>
          </div>
          {{#if errors.email}}
            <p class="help is-danger">{{errors.email.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <label class="label">Password {{#if user}}<span class="has-text-grey">(Leave blank to keep current password)</span>{{/if}}</label>
          <div class="control has-icons-left">
            <input 
              class="input {{#if errors.password}}is-danger{{/if}}" 
              type="password" 
              name="password" 
              placeholder="{{#if user}}New password{{else}}Password{{/if}}"
              {{#unless user}}required{{/unless}}
              minlength="6"
            >
            <span class="icon is-small is-left">
              <i class="fas fa-lock"></i>
            </span>
          </div>
          {{#if errors.password}}
            <p class="help is-danger">{{errors.password.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <label class="label">Confirm Password</label>
          <div class="control has-icons-left">
            <input 
              class="input {{#if errors.password2}}is-danger{{/if}}" 
              type="password" 
              name="password2" 
              placeholder="Confirm password"
              {{#unless user}}required{{/unless}}
              minlength="6"
            >
            <span class="icon is-small is-left">
              <i class="fas fa-lock"></i>
            </span>
          </div>
          {{#if errors.password2}}
            <p class="help is-danger">{{errors.password2.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <label class="label">Role</label>
          <div class="control">
            <div class="select is-fullwidth {{#if errors.role}}is-danger{{/if}}">
              <select name="role" required>
                <option value="user" {{#if (or (eq user.role 'user') (eq formData.role 'user'))}}selected{{/if}}>User</option>
                <option value="admin" {{#if (or (eq user.role 'admin') (eq formData.role 'admin'))}}selected{{/if}}>Administrator</option>
              </select>
            </div>
          </div>
          {{#if errors.role}}
            <p class="help is-danger">{{errors.role.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <label class="label">Storage Quota (MB)</label>
          <div class="control">
            <input 
              class="input {{#if errors.storage_quota}}is-danger{{/if}}" 
              type="number" 
              name="storage_quota" 
              min="10" 
              step="1"
              value="{{#if user}}{{user.storage_quota}}{{else}}{{formData.storage_quota}}{{/if}}"
              required
            >
          </div>
          <p class="help">Maximum storage space in MB. Default is 100 MB.</p>
          {{#if errors.storage_quota}}
            <p class="help is-danger">{{errors.storage_quota.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" name="is_active" {{#if (or (eq user.is_active true) (eq formData.is_active 'true'))}}checked{{/if}}>
              Account is active (user can log in)
            </label>
          </div>
        </div>
        
        <div class="field is-grouped">
          <div class="control">
            <button type="submit" class="button is-primary">
              <span class="icon">
                <i class="fas fa-save"></i>
              </span>
              <span>Save User</span>
            </button>
          </div>
          <div class="control">
            <a href="/admin/users" class="button is-light">
              <span class="icon">
                <i class="fas fa-times"></i>
              </span>
              <span>Cancel</span>
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

{{#content "scripts"}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Close notification when clicking the delete button
    document.querySelectorAll('.notification .delete').forEach(deleteButton => {
      deleteButton.addEventListener('click', function() {
        this.closest('.notification').remove();
      });
    });
    
    // Password confirmation validation
    const password = document.querySelector('input[name=password]');
    const confirmPassword = document.querySelector('input[name=password2]');
    
    function validatePassword() {
      if (password.value !== confirmPassword.value) {
        confirmPassword.setCustomValidity("Passwords don't match");
      } else {
        confirmPassword.setCustomValidity('');
      }
    }
    
    if (password && confirmPassword) {
      password.onchange = validatePassword;
      confirmPassword.onkeyup = validatePassword;
    }
  });
</script>
{{/content}}
