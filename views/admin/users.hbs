{{!< ../layout}}

<div class="level">
  <div class="level-left">
    <h1 class="title">Manage Users</h1>
  </div>
  <div class="level-right">
    <div class="buttons">
      <a href="/admin/users/new" class="button is-primary">
        <span class="icon">
          <i class="fas fa-user-plus"></i>
        </span>
        <span>Add User</span>
      </a>
    </div>
  </div>
</div>

{{#if success_msg}}
  <div class="notification is-success">
    <button class="delete"></button>
    {{success_msg}}
  </div>
{{/if}}

{{#if error_msg}}
  <div class="notification is-danger">
    <button class="delete"></button>
    {{error_msg}}
  </div>
{{/if}}

<div class="box">
  <div class="table-container">
    <table class="table is-fullwidth is-hoverable">
      <thead>
        <tr>
          <th>Name</th>
          <th>Email</th>
          <th>Role</th>
          <th>Storage Used</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {{#each users}}
          <tr>
            <td>
              <div class="media">
                <div class="media-left">
                  <figure class="image is-32x32">
                    <img 
                      src="https://ui-avatars.com/api/?name={{name}}&background=random" 
                      alt="{{name}}"
                      class="is-rounded"
                    >
                  </figure>
                </div>
                <div class="media-content">
                  <p class="has-text-weight-semibold">{{name}}</p>
                  <p class="is-size-7 has-text-grey">ID: {{id}}</p>
                </div>
              </div>
            </td>
            <td>{{email}}</td>
            <td>
              <span class="tag {{#if (eq role 'admin')}}is-danger{{else}}is-info{{/if}}">
                {{role}}
              </span>
            </td>
            <td>
              <div class="content">
                <p>{{getFormattedSize storage_used}} of {{getFormattedSize storage_quota}}</p>
                <progress 
                  class="progress is-small {{#if (gt (getStoragePercentage storage_used storage_quota) 90)}}is-danger{{else if (gt (getStoragePercentage storage_used storage_quota) 70)}}is-warning{{else}}is-primary{{/if}}" 
                  value="{{storage_used}}" 
                  max="{{storage_quota}}"
                >
                  {{getStoragePercentage storage_used storage_quota}}%
                </progress>
                <p class="help">{{getStoragePercentage storage_used storage_quota}}% used</p>
              </div>
            </td>
            <td>
              <span class="tag {{#if is_active}}is-success{{else}}is-light{{/if}}">
                {{#if is_active}}Active{{else}}Inactive{{/if}}
              </span>
            </td>
            <td>
              <div class="buttons are-small">
                <a href="/admin/users/{{id}}/edit" class="button is-info is-light">
                  <span class="icon">
                    <i class="fas fa-edit"></i>
                  </span>
                  <span>Edit</span>
                </a>
                {{#unless (eq id ../currentUser.id)}}
                  {{#if is_active}}
                    <button class="button is-warning is-light toggle-status" data-id="{{id}}" data-status="false">
                      <span class="icon">
                        <i class="fas fa-user-slash"></i>
                      </span>
                      <span>Deactivate</span>
                    </button>
                  {{else}}
                    <button class="button is-success is-light toggle-status" data-id="{{id}}" data-status="true">
                      <span class="icon">
                        <i class="fas fa-user-check"></i>
                      </span>
                      <span>Activate</span>
                    </button>
                  {{/if}}
                  <button class="button is-danger is-light delete-user" data-id="{{id}}">
                    <span class="icon">
                      <i class="fas fa-trash"></i>
                    </span>
                    <span>Delete</span>
                  </button>
                {{/unless}}
              </div>
            </td>
          </tr>
        {{else}}
          <tr>
            <td colspan="6" class="has-text-centered">
              <div class="has-text-centered py-5">
                <span class="icon is-large has-text-grey-light">
                  <i class="fas fa-users fa-3x"></i>
                </span>
                <p class="has-text-grey">No users found</p>
              </div>
            </td>
          </tr>
        {{/each}}
      </tbody>
    </table>
  </div>
</div>

<!-- Delete User Modal -->
<div class="modal" id="deleteUserModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Confirm Deletion</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <p>Are you sure you want to delete this user? This action cannot be undone.</p>
      <p class="has-text-weight-bold mt-3">All documents associated with this user will also be deleted.</p>
    </section>
    <footer class="modal-card-foot">
      <form id="deleteUserForm" method="POST" action="">
        <input type="hidden" name="_method" value="DELETE">
        <button type="button" class="button is-light cancel-delete">Cancel</button>
        <button type="submit" class="button is-danger">Delete User</button>
      </form>
    </footer>
  </div>
</div>

<!-- Toggle Status Modal -->
<div class="modal" id="toggleStatusModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Confirm Status Change</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <p id="statusMessage"></p>
    </section>
    <footer class="modal-card-foot">
      <form id="toggleStatusForm" method="POST" action="">
        <input type="hidden" name="_method" value="PATCH">
        <input type="hidden" name="is_active" value="">
        <button type="button" class="button is-light cancel-toggle">Cancel</button>
        <button type="submit" class="button is-warning">Confirm</button>
      </form>
    </footer>
  </div>
</div>

{{#content "scripts"}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Delete user modal
    const deleteButtons = document.querySelectorAll('.delete-user');
    const deleteModal = document.getElementById('deleteUserModal');
    const deleteForm = document.getElementById('deleteUserForm');
    const cancelDelete = document.querySelector('.cancel-delete');
    
    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const userId = this.getAttribute('data-id');
        deleteForm.action = `/admin/users/${userId}`;
        deleteModal.classList.add('is-active');
      });
    });
    
    // Close modal when clicking the background or close button
    deleteModal.querySelector('.modal-background').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });
    
    deleteModal.querySelector('.delete').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });
    
    cancelDelete.addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });
    
    // Toggle status modal
    const toggleButtons = document.querySelectorAll('.toggle-status');
    const toggleModal = document.getElementById('toggleStatusModal');
    const toggleForm = document.getElementById('toggleStatusForm');
    const statusMessage = document.getElementById('statusMessage');
    const cancelToggle = document.querySelector('.cancel-toggle');
    
    toggleButtons.forEach(button => {
      button.addEventListener('click', function() {
        const userId = this.getAttribute('data-id');
        const newStatus = this.getAttribute('data-status') === 'true';
        const userName = this.closest('tr').querySelector('.has-text-weight-semibold').textContent;
        
        toggleForm.action = `/admin/users/${userId}/status`;
        toggleForm.querySelector('input[name="is_active"]').value = newStatus;
        
        if (newStatus) {
          statusMessage.textContent = `Are you sure you want to activate the user "${userName}"? This will allow them to log in again.`;
          toggleForm.querySelector('button[type="submit"]').className = 'button is-success';
          toggleForm.querySelector('button[type="submit"]').textContent = 'Activate User';
        } else {
          statusMessage.textContent = `Are you sure you want to deactivate the user "${userName}"? They will not be able to log in until reactivated.`;
          toggleForm.querySelector('button[type="submit"]').className = 'button is-warning';
          toggleForm.querySelector('button[type="submit"]').textContent = 'Deactivate User';
        }
        
        toggleModal.classList.add('is-active');
      });
    });
    
    // Close modal when clicking the background or close button
    toggleModal.querySelector('.modal-background').addEventListener('click', () => {
      toggleModal.classList.remove('is-active');
    });
    
    toggleModal.querySelector('.delete').addEventListener('click', () => {
      toggleModal.classList.remove('is-active');
    });
    
    cancelToggle.addEventListener('click', () => {
      toggleModal.classList.remove('is-active');
    });
    
    // Close all notifications when clicking the delete button
    document.querySelectorAll('.notification .delete').forEach(deleteButton => {
      deleteButton.addEventListener('click', function() {
        this.closest('.notification').remove();
      });
    });
  });
</script>
{{/content}}
