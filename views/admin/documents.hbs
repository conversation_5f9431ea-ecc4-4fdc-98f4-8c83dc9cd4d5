{{!< ../layout}}

<div class="level">
  <div class="level-left">
    <h1 class="title">Manage Documents</h1>
  </div>
  <div class="level-right">
    <div class="field has-addons">
      <div class="control
        <div class="select">
          <select id="filterUser">
            <option value="">All Users</option>
            {{#each users}}
              <option value="{{id}}" {{#if (eq id ../currentFilter.userId)}}selected{{/if}}>{{name}} ({{email}})</option>
            {{/each}}
          </select>
        </div>
      </div>
      <div class="control">
        <div class="select">
          <select id="filterType">
            <option value="">All Types</option>
            <option value="application/pdf" {{#if (eq currentFilter.type 'application/pdf')}}selected{{/if}}>PDF</option>
            <option value="word" {{#if (eq currentFilter.type 'word')}}selected{{/if}}>Word</option>
            <option value="image" {{#if (eq currentFilter.type 'image')}}selected{{/if}}>Image</option>
            <option value="other" {{#if (eq currentFilter.type 'other')}}selected{{/if}}>Other</option>
          </select>
        </div>
      </div>
      <div class="control">
        <a href="/admin/documents" id="applyFilters" class="button is-info">
          <span class="icon">
            <i class="fas fa-filter"></i>
          </span>
          <span>Filter</span>
        </a>
      </div>
    </div>
  </div>
</div>

{{#if success_msg}}
  <div class="notification is-success">
    <button class="delete"></button>
    {{success_msg}}
  </div>
{{/if}}

{{#if error_msg}}
  <div class="notification is-danger">
    <button class="delete"></button>
    {{error_msg}}
  </div>
{{/if}}

<div class="box">
  {{#if documents.length}}
    <div class="table-container">
      <table class="table is-fullwidth is-hoverable">
        <thead>
          <tr>
            <th>Name</th>
            <th>Uploaded By</th>
            <th>Type</th>
            <th>Size</th>
            <th>Uploaded</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {{#each documents}}
            {{> document-table-row this}}
          {{/each}}
        </tbody>
      </table>
    </div>

    {{#if pagination.pages.length}}
      <nav class="pagination is-centered" role="navigation" aria-label="pagination">
        {{#if (gt pagination.currentPage 1)}}
          <a href="?page={{math pagination.currentPage '-' 1}}{{#if currentFilter.userId}}&user={{currentFilter.userId}}{{/if}}{{#if currentFilter.type}}&type={{currentFilter.type}}{{/if}}" class="pagination-previous">Previous</a>
        {{else}}
          <a class="pagination-previous" disabled>Previous</a>
        {{/if}}

        {{#if (lt pagination.currentPage pagination.totalPages)}}
          <a href="?page={{math pagination.currentPage '+' 1}}{{#if currentFilter.userId}}&user={{currentFilter.userId}}{{/if}}{{#if currentFilter.type}}&type={{currentFilter.type}}{{/if}}" class="pagination-next">Next</a>
        {{else}}
          <a class="pagination-next" disabled>Next</a>
        {{/if}}

        <ul class="pagination-list">
          {{#each pagination.pages}}
            {{#if (eq this '...')}}
              <li><span class="pagination-ellipsis">&hellip;</span></li>
            {{else}}
              <li>
                <a
                  href="?page={{this}}{{#if ../currentFilter.userId}}&user={{../currentFilter.userId}}{{/if}}{{#if ../currentFilter.type}}&type={{../currentFilter.type}}{{/if}}"
                  class="pagination-link {{#if (eq this ../pagination.currentPage)}}is-current{{/if}}"
                  aria-label="Goto page {{this}}"
                >
                  {{this}}
                </a>
              </li>
            {{/if}}
          {{/each}}
        </ul>
      </nav>
    {{/if}}

  {{else}}
    <div class="has-text-centered py-5">
      <span class="icon is-large has-text-grey-light">
        <i class="fas fa-file-upload fa-3x"></i>
      </span>
      <p class="has-text-grey">No documents found</p>
      {{#if (or currentFilter.userId currentFilter.type)}}
        <p class="has-text-grey is-size-7 mt-2">Try adjusting your filters</p>
        <a href="/admin/documents" class="button is-light is-small mt-3">
          <span class="icon">
            <i class="fas fa-times"></i>
          </span>
          <span>Clear Filters</span>
        </a>
      {{/if}}
    </div>
  {{/if}}
</div>

<!-- Delete Document Modal -->
<div class="modal" id="deleteDocumentModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Delete Document</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <p>Are you sure you want to delete <strong id="documentName"></strong>?</p>
      <p class="has-text-danger mt-3">This action cannot be undone.</p>
    </section>
    <footer class="modal-card-foot">
      <form id="deleteDocumentForm" method="POST" action="">
        <input type="hidden" name="_method" value="DELETE">
        <button type="button" class="button is-light cancel-delete">Cancel</button>
        <button type="submit" class="button is-danger">Delete</button>
      </form>
    </footer>
  </div>
</div>

{{#content "scripts"}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Delete document modal
    const deleteButtons = document.querySelectorAll('.delete-document');
    const deleteModal = document.getElementById('deleteDocumentModal');
    const deleteForm = document.getElementById('deleteDocumentForm');
    const documentName = document.getElementById('documentName');
    const cancelDelete = document.querySelector('.cancel-delete');

    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const docId = this.getAttribute('data-id');
        const docName = this.getAttribute('data-name');

        documentName.textContent = docName;
        deleteForm.action = `/admin/documents/${docId}`;
        deleteModal.classList.add('is-active');
      });
    });

    // Close modal when clicking the background or close button
    deleteModal.querySelector('.modal-background').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });

    deleteModal.querySelector('.delete').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });

    cancelDelete.addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });

    // Close notifications when clicking the delete button
    document.querySelectorAll('.notification .delete').forEach(deleteButton => {
      deleteButton.addEventListener('click', function() {
        this.closest('.notification').remove();
      });
    });

    // Apply filters
    const applyFilters = document.getElementById('applyFilters');
    const filterUser = document.getElementById('filterUser');
    const filterType = document.getElementById('filterType');

    applyFilters.addEventListener('click', function(e) {
      e.preventDefault();

      let url = '/admin/documents?';
      const params = [];

      if (filterUser.value) {
        params.push(`user=${filterUser.value}`);
      }

      if (filterType.value) {
        params.push(`type=${filterType.value}`);
      }

      window.location.href = url + params.join('&');
    });
  });
</script>
{{/content}}
