{{!--
  Document Table Row Partial

  Parameters:
  - context: 'user' | 'admin' | 'dashboard' - determines which columns and actions to show
  - showActions: boolean - whether to show the actions column (default: true)
--}}

<!-- DEBUG: context={{context}}, showActions={{showActions}} -->
<tr>
  <!-- Document Name Column -->
  <td>
    {{#if (eq context 'dashboard')}}
      <a href="/documents/{{id}}" class="has-text-dark">
    {{/if}}
    {{#if (eq context 'admin')}}
      <a href="/documents/{{id}}" class="has-text-dark">
    {{/if}}

    <span class="icon-text">
      <span class="icon">
        {{#if (eq context 'user')}}
          <i class="fas {{fileIcon mime_type}}"></i>
        {{else}}
          {{!-- Admin and dashboard use more detailed file type icons --}}
          {{#if (eq mime_type 'application/pdf')}}
            <i class="fas fa-file-pdf has-text-danger"></i>
          {{else if (or (eq mime_type 'application/msword') (eq mime_type 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'))}}
            <i class="fas fa-file-word has-text-info"></i>
          {{else if (or (eq mime_type 'application/vnd.ms-excel') (eq mime_type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'))}}
            <i class="fas fa-file-excel has-text-success"></i>
          {{else if (eq mime_type 'text/plain')}}
            <i class="fas fa-file-alt"></i>
          {{else if (eq (split mime_type '/')[0] 'image')}}
            <i class="fas fa-file-image"></i>
          {{else}}
            <i class="fas fa-file"></i>
          {{/if}}
        {{/if}}
      </span>
      <span {{#if (eq context 'user')}}class="truncate-text" title="{{name}}"{{/if}}>{{name}}</span>
    </span>

      </a>
  </td>

  <!-- Author/Uploaded By Column -->
  {{#if (eq context 'user')}}
    <td>{{authors}}</td>
  {{/if}}
  {{#if (eq context 'admin')}}
    <td>
      <a href="/admin/users/{{User.id}}">{{User.name}}</a>
      <span class="is-size-7 has-text-grey is-block">{{User.email}}</span>
    </td>
  {{/if}}
  {{#if (eq context 'dashboard')}}
    <td>{{User.email}}</td>
  {{/if}}

  <!-- Type Column -->
  {{#if (eq context 'user')}}
    <td>{{mime_type}}</td>
  {{/if}}
  {{#if (eq context 'admin')}}
    <td>
      <span class="tag is-light">{{mime_type}}</span>
    </td>
  {{/if}}

  <!-- Size Column -->
  {{#if (eq context 'user')}}
    <td>{{formatFileSize size_bytes}}</td>
  {{else}}
    <td>{{getFormattedSize this}}</td>
  {{/if}}

  <!-- Date Column -->
  {{#if (eq context 'user')}}
    <td>{{formatDate createdAt 'YYYY-MM-DD'}}</td>
  {{else}}
    <td>{{formatDate created_at}}</td>
  {{/if}}

  <!-- Actions Column -->
  {{#if showActions}}
    {{#if (ne context 'dashboard')}}
      <td {{#if (eq context 'user')}}class="has-text-centered"{{/if}}>
        {{#if (eq context 'user')}}
          <div class="buttons is-justify-content-center">
            <a href="/documents/{{id}}/view" class="button is-small is-info" title="View" target="_blank">
              <span class="icon">
                <i class="fas fa-eye"></i>
              </span>
            </a>
            <button class="button is-small is-warning edit-document" data-id="{{id}}" data-name="{{name}}" data-authors="{{authors}}" title="Edit">
              <span class="icon">
                <i class="fas fa-edit"></i>
              </span>
            </button>
            <a href="/documents/{{id}}/download" class="button is-small is-success" title="Download">
              <span class="icon">
                <i class="fas fa-download"></i>
              </span>
            </a>
            <button class="button is-small is-danger delete-document" data-id="{{id}}" title="Delete">
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
            </button>
          </div>
        {{/if}}
        {{#if (eq context 'admin')}}
          <div class="buttons are-small">
            <a href="/documents/{{id}}/download" class="button is-info is-light" title="Download">
              <span class="icon">
                <i class="fas fa-download"></i>
              </span>
            </a>
            <a href="/documents/{{id}}" class="button is-link is-light" title="View Details">
              <span class="icon">
                <i class="fas fa-eye"></i>
              </span>
            </a>
            <button class="button is-danger is-light delete-document" data-id="{{id}}" data-name="{{name}}" title="Delete">
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
            </button>
          </div>
        {{/if}}
      </td>
    {{/if}}
  {{/if}}
</tr>
