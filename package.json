{"name": "personal-library-management", "version": "1.0.0", "private": true, "scripts": {"start": "node ./bin/www", "dev": "nodemon ./bin/www", "init-db": "node scripts/init-db.js"}, "dependencies": {"bcryptjs": "^2.4.3", "connect-flash": "^0.1.1", "cookie-parser": "~1.4.4", "debug": "~2.6.9", "express": "~4.16.1", "express-session": "^1.17.3", "express-validator": "^6.14.2", "hbs": "~4.0.4", "http-errors": "~1.6.3", "method-override": "^3.0.0", "morgan": "~1.9.1", "multer": "^1.4.5-lts.1", "passport": "^0.6.0", "passport-local": "^1.0.0", "sequelize": "^6.28.0", "sequelize-cli": "^6.6.0", "sqlite3": "^5.1.4"}, "devDependencies": {"nodemon": "^2.0.20"}}