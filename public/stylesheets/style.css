/* Minimal custom styles that enhance Bulma */

/* Ensure full viewport height for the main content */
html, body, #app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Main content takes available space */
.main-content {
  flex: 1;
}

/* Hero with gradient background */
.hero.is-primary {
  background: linear-gradient(135deg, #00d1b2 0%, #209cee 100%);
}

/* File type colors */
.has-text-pdf { color: #e74c3c; }
.has-text-word { color: #2b579a; }
.has-text-excel { color: #217346; }
.has-text-image { color: #e67e22; }
.has-text-archive { color: #8e44ad; }

/* Responsive table */
.table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Ensure modals are centered */
.modal-card {
  margin: 0 auto;
  max-height: calc(100vh - 40px);
}

/* Add some spacing to the footer */
.footer {
  padding: 3rem 1.5rem;
}
