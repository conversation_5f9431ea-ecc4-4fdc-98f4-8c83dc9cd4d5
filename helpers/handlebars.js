const moment = require('moment');

module.exports = {
  // Format date helper
  formatDate: function(date, format) {
    return moment(date).format(format || 'YYYY-MM-DD');
  },
  
  // Equal comparison helper
  eq: function(a, b) {
    return a === b;
  },
  
  // Not equal comparison helper
  neq: function(a, b) {
    return a !== b;
  },
  
  // Greater than helper
  gt: function(a, b) {
    return a > b;
  },
  
  // Greater than or equal helper
  gte: function(a, b) {
    return a >= b;
  },
  
  // Less than helper
  lt: function(a, b) {
    return a < b;
  },
  
  // Less than or equal helper
  lte: function(a, b) {
    return a <= b;
  },
  
  // Logical OR helper
  or: function() {
    for (let i = 0; i < arguments.length - 1; i++) {
      if (arguments[i]) {
        return true;
      }
    }
    return false;
  },
  
  // Logical AND helper
  and: function() {
    for (let i = 0; i < arguments.length - 1; i++) {
      if (!arguments[i]) {
        return false;
      }
    }
    return true;
  },
  
  // Split string helper
  split: function(str, separator) {
    if (typeof str !== 'string') return [];
    return str.split(separator);
  },
  
  // Format file size helper
  formatFileSize: function(document) {
    const bytes = document.size_bytes;
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  },
  
  // File icon helper based on mime type
  fileIcon: function(mimeType) {
    if (mimeType === 'application/pdf') {
      return 'fa-file-pdf';
    } else if (mimeType === 'application/msword' || mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return 'fa-file-word';
    } else if (mimeType === 'application/vnd.ms-excel' || mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return 'fa-file-excel';
    } else if (mimeType === 'text/plain') {
      return 'fa-file-alt';
    } else if (mimeType && mimeType.startsWith('image/')) {
      return 'fa-file-image';
    } else {
      return 'fa-file';
    }
  }
};
